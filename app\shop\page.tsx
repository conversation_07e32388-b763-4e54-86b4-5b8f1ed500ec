'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import ProductCard from '@/components/ProductCard'
import { Filter } from 'lucide-react'

// Mock products data
const products = [
  { id: '1', name: 'Silk Evening Dress', price: 299, image: 'https://picsum.photos/400/500?random=3', category: 'Dresses' },
  { id: '2', name: 'Cashmere Blazer', price: 459, image: 'https://picsum.photos/400/500?random=4', category: 'Outerwear' },
  { id: '3', name: '<PERSON> Necklace', price: 189, image: 'https://picsum.photos/400/500?random=5', category: 'Accessories' },
  { id: '4', name: 'Leather Handbag', price: 329, image: 'https://picsum.photos/400/500?random=6', category: 'Bags' },
  { id: '5', name: 'Wool Coat', price: 599, image: 'https://picsum.photos/400/500?random=7', category: 'Outerwear' },
  { id: '6', name: 'Diamond Earrings', price: 899, image: 'https://picsum.photos/400/500?random=8', category: 'Accessories' },
  { id: '7', name: 'Cock<PERSON> Dress', price: 249, image: 'https://picsum.photos/400/500?random=9', category: 'Dresses' },
  { id: '8', name: 'Silk Scarf', price: 129, image: 'https://picsum.photos/400/500?random=10', category: 'Accessories' },
]

const categories = ['All', 'Dresses', 'Outerwear', 'Accessories', 'Bags']

export default function ShopPage() {
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [showFilters, setShowFilters] = useState(false)

  const filteredProducts = selectedCategory === 'All' 
    ? products 
    : products.filter(product => product.category === selectedCategory)

  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="text-4xl md:text-5xl font-serif font-bold text-gold mb-4">
            Shop
          </h1>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            Discover our complete range of luxury fashion pieces, each designed to embody natural elegance.
          </p>
        </motion.div>

        {/* Filters */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gold">
              {filteredProducts.length} Products
            </h2>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="md:hidden flex items-center space-x-2 text-gold"
            >
              <Filter className="w-5 h-5" />
              <span>Filters</span>
            </button>
          </div>

          <div className={`${showFilters ? 'block' : 'hidden'} md:block`}>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-full border transition-all duration-300 ${
                    selectedCategory === category
                      ? 'bg-gold text-black border-gold'
                      : 'border-gold text-gold hover:bg-gold hover:text-black'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
          layout
        >
          {filteredProducts.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              layout
            >
              <ProductCard product={product} />
            </motion.div>
          ))}
        </motion.div>

        {/* Load More Button */}
        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <button className="btn-secondary">
            Load More Products
          </button>
        </motion.div>
      </div>
    </div>
  )
}