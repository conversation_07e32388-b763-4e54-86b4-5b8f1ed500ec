"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_ProductCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProductCard */ \"(app-pages-browser)/./components/ProductCard.tsx\");\n/* harmony import */ var _components_ui_jooka_hero_demo__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/jooka-hero-demo */ \"(app-pages-browser)/./components/ui/jooka-hero-demo.tsx\");\n/* harmony import */ var _components_CategoriesShowcase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/CategoriesShowcase */ \"(app-pages-browser)/./components/CategoriesShowcase.tsx\");\n/* harmony import */ var _components_TestimonialsSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/TestimonialsSection */ \"(app-pages-browser)/./components/TestimonialsSection.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Mock featured products data\nconst featuredProducts = [\n    {\n        id: \"1\",\n        name: \"Silk Evening Dress\",\n        price: 299,\n        image: \"https://via.placeholder.com/400x500/1a1a1a/d4af37?text=Silk+Evening+Dress\",\n        category: \"Dresses\"\n    },\n    {\n        id: \"2\",\n        name: \"Cashmere Blazer\",\n        price: 459,\n        image: \"https://via.placeholder.com/400x500/1a1a1a/d4af37?text=Cashmere+Blazer\",\n        category: \"Outerwear\"\n    },\n    {\n        id: \"3\",\n        name: \"Pearl Necklace\",\n        price: 189,\n        image: \"https://via.placeholder.com/400x500/1a1a1a/d4af37?text=Pearl+Necklace\",\n        category: \"Accessories\"\n    },\n    {\n        id: \"4\",\n        name: \"Leather Handbag\",\n        price: 329,\n        image: \"https://via.placeholder.com/400x500/1a1a1a/d4af37?text=Leather+Handbag\",\n        category: \"Bags\"\n    }\n];\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_jooka_hero_demo__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-24 px-8 md:px-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-b from-black via-black/98 to-black\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                className: \"text-center mb-20\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 40\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    ease: \"easeOut\"\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.2\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"inline-block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium tracking-[0.2em] text-gold/60 uppercase mb-4 block\",\n                                            children: \"Curated Excellence\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2, {\n                                        className: \"text-5xl md:text-6xl lg:text-7xl font-serif font-light text-gold mb-8 tracking-tight\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.3\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: [\n                                            \"Featured\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-4xl md:text-5xl lg:text-6xl text-ivory/90 font-light italic\",\n                                                children: \"Collection\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            width: 0\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            width: \"4rem\"\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.5\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"h-px bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mb-8\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                        className: \"text-lg md:text-xl text-ivory/70 max-w-3xl mx-auto leading-relaxed font-light\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.6\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: [\n                                            \"Discover our carefully curated selection of luxury pieces that embody\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold/80 font-medium\",\n                                                children: \" timeless elegance\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" and\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold/80 font-medium\",\n                                                children: \" natural beauty\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, this),\n                                            \".\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12\",\n                                initial: {\n                                    opacity: 0\n                                },\n                                whileInView: {\n                                    opacity: 1\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: featuredProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 60,\n                                            scale: 0.9\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.9 + index * 0.15,\n                                            ease: \"easeOut\"\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            product: product\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, product.id, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                className: \"text-center mt-20\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 1.4\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/shop\",\n                                        className: \"inline-flex items-center px-8 py-4 text-sm font-medium tracking-[0.1em] text-gold border-2 border-gold/30 hover:border-gold hover:bg-gold hover:text-black transition-all duration-500 uppercase group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Explore Collection\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.span, {\n                                                className: \"ml-2 group-hover:ml-4 transition-all duration-300\",\n                                                initial: {\n                                                    x: 0\n                                                },\n                                                whileHover: {\n                                                    x: 4\n                                                },\n                                                children: \"→\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CategoriesShowcase__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-24 px-8 md:px-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-black via-charcoal/20 to-black\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 max-w-7xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 lg:gap-20 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"space-y-8\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: -60\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        ease: \"easeOut\"\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.2\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium tracking-[0.2em] text-gold/60 uppercase mb-4 block\",\n                                                    children: \"Heritage\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-6xl font-serif font-light text-gold mb-6 tracking-tight\",\n                                                    children: [\n                                                        \"Our\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"block text-4xl md:text-5xl text-ivory/90 font-light italic\",\n                                                            children: \"Story\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                width: 0\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                width: \"4rem\"\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.4\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"h-px bg-gradient-to-r from-gold via-gold/50 to-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            className: \"space-y-6\",\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.5\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl md:text-2xl text-ivory/80 leading-relaxed font-light\",\n                                                    children: [\n                                                        \"JOOKA was born from a vision to create fashion that transcends trends and embraces\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gold/80 font-medium\",\n                                                            children: \" timeless elegance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \".\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg text-ivory/70 leading-relaxed font-light\",\n                                                    children: \"Our commitment to natural beauty and sophisticated design reflects in every piece we create. Each garment is crafted with meticulous attention to detail, using only the finest materials sourced ethically from around the world.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base text-ivory/60 leading-relaxed font-light\",\n                                                    children: \"We believe that true luxury lies not in excess, but in the perfect harmony of craftsmanship, sustainability, and timeless design.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.7\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                transition: {\n                                                    duration: 0.2\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    href: \"/about\",\n                                                    className: \"inline-flex items-center px-8 py-4 text-sm font-medium tracking-[0.1em] text-gold border-2 border-gold/30 hover:border-gold hover:bg-gold hover:text-black transition-all duration-500 uppercase group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Discover Our Heritage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.span, {\n                                                            className: \"ml-2 group-hover:ml-4 transition-all duration-300\",\n                                                            initial: {\n                                                                x: 0\n                                                            },\n                                                            whileHover: {\n                                                                x: 4\n                                                            },\n                                                            children: \"→\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"relative\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: 60\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        ease: \"easeOut\"\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-[500px] lg:h-[600px] overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: \"https://via.placeholder.com/600x700/1a1a1a/d4af37?text=JOOKA+Story\",\n                                                alt: \"JOOKA Story - Craftsmanship and Elegance\",\n                                                fill: true,\n                                                className: \"object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                className: \"absolute inset-0 border-2 border-gold/20\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    scale: 1.1\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    scale: 1\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    delay: 0.3\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                className: \"absolute bottom-8 left-8 bg-black/70 backdrop-blur-sm p-6 border border-gold/30\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    delay: 0.8\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-serif font-bold text-gold mb-1\",\n                                                            children: \"2019\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-ivory/70 font-light tracking-wide\",\n                                                            children: \"Founded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                className: \"absolute top-8 right-8 bg-black/70 backdrop-blur-sm p-6 border border-gold/30\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: -20\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    delay: 1\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-serif font-bold text-gold mb-1\",\n                                                            children: \"100%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-ivory/70 font-light tracking-wide\",\n                                                            children: \"Sustainable\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TestimonialsSection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\app\\\\page.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});