'use client'

import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { ShoppingCart } from 'lucide-react'
import { useCartStore } from '@/store/cartStore'

interface Product {
  id: string
  name: string
  price: number
  image: string
  category?: string
}

interface ProductCardProps {
  product: Product
}

export default function ProductCard({ product }: ProductCardProps) {
  const addItem = useCartStore((state) => state.addItem)

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault()
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
    })
  }

  return (
    <motion.div
      className="group relative bg-black/40 backdrop-blur-sm border border-gold/10 hover:border-gold/30 transition-all duration-500 overflow-hidden"
      whileHover={{ y: -8, scale: 1.02 }}
      transition={{ duration: 0.4, ease: "easeOut" }}
    >
      <Link href={`/product/${product.id}`}>
        {/* Image Container */}
        <div className="relative aspect-[4/5] overflow-hidden">
          <Image
            src={product.image}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
          />

          {/* Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-transparent" />

          {/* Category Badge */}
          {product.category && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="absolute top-4 left-4 bg-black/70 backdrop-blur-sm px-3 py-1 rounded-full"
            >
              <span className="text-gold/90 text-xs font-medium tracking-wider uppercase">
                {product.category}
              </span>
            </motion.div>
          )}

          {/* Quick Add Button */}
          <motion.button
            onClick={handleAddToCart}
            className="absolute bottom-4 right-4 bg-gold/90 hover:bg-gold text-black p-3 rounded-full backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-all duration-300 hover:scale-110 shadow-lg"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
          >
            <ShoppingCart className="w-4 h-4" />
          </motion.button>

          {/* Hover Overlay with Product Info */}
          <motion.div
            className="absolute inset-x-0 bottom-0 p-6 text-white opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0"
            initial={{ opacity: 0, y: 20 }}
            whileHover={{ opacity: 1, y: 0 }}
          >
            <div className="space-y-2">
              <h3 className="text-lg font-serif font-medium text-gold">
                {product.name}
              </h3>
              <p className="text-sm text-ivory/80 font-light">
                Premium quality craftsmanship
              </p>
            </div>
          </motion.div>
        </div>

        {/* Product Info */}
        <div className="p-6 space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-ivory font-serif font-medium text-lg group-hover:text-gold transition-colors duration-300">
              {product.name}
            </h3>
            <motion.div
              className="w-2 h-2 bg-gold rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              initial={{ scale: 0 }}
              whileHover={{ scale: 1 }}
            />
          </div>

          <div className="flex items-center justify-between">
            <p className="text-gold font-semibold text-xl tracking-wide">
              ${product.price}
            </p>
            <motion.span
              className="text-ivory/60 text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              initial={{ x: -10 }}
              whileHover={{ x: 0 }}
            >
              View Details →
            </motion.span>
          </div>

          {/* Decorative Line */}
          <motion.div
            className="h-px bg-gradient-to-r from-transparent via-gold/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            initial={{ scaleX: 0 }}
            whileHover={{ scaleX: 1 }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </Link>
    </motion.div>
  )
}