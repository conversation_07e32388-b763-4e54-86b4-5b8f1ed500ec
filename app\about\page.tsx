'use client'

import Image from 'next/image'
import { motion } from 'framer-motion'

export default function AboutPage() {
  return (
    <div className="min-h-screen py-16">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="text-5xl font-serif font-bold text-gold mb-6">
            About JOOKA
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Natural Elegance
          </p>
        </motion.div>

        {/* Story Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h2 className="text-3xl font-serif font-bold text-gold mb-6">
              Our Story
            </h2>
            <p className="text-gray-300 text-lg mb-6 leading-relaxed">
              JOOKA was founded with a simple yet profound vision: to create fashion that celebrates the inherent elegance found in nature and the human form. Our name, derived from the ancient word for "natural beauty," reflects our commitment to timeless design and sustainable practices.
            </p>
            <p className="text-gray-300 text-lg mb-6 leading-relaxed">
              Every piece in our collection is thoughtfully designed to enhance your natural grace while respecting the environment. We believe that true luxury lies not in excess, but in the perfect harmony between form, function, and conscience.
            </p>
            <p className="text-gray-300 text-lg leading-relaxed">
              From our atelier to your wardrobe, each garment tells a story of craftsmanship, sustainability, and the timeless pursuit of natural elegance.
            </p>
          </motion.div>
          
          <motion.div
            className="relative h-96 lg:h-[500px]"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <Image
              src="https://picsum.photos/600/500?random=14"
              alt="JOOKA Atelier"
              fill
              className="object-cover rounded-lg"
            />
          </motion.div>
        </div>

        {/* Values Section */}
        <motion.div
          className="bg-charcoal/30 rounded-lg p-8 mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <h2 className="text-3xl font-serif font-bold text-gold mb-8 text-center">
            Our Values
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <h3 className="text-xl font-serif font-bold text-gold mb-4">
                Sustainability
              </h3>
              <p className="text-gray-300">
                We source materials ethically and employ sustainable practices throughout our production process.
              </p>
            </div>
            <div className="text-center">
              <h3 className="text-xl font-serif font-bold text-gold mb-4">
                Craftsmanship
              </h3>
              <p className="text-gray-300">
                Each piece is meticulously crafted by skilled artisans who share our passion for excellence.
              </p>
            </div>
            <div className="text-center">
              <h3 className="text-xl font-serif font-bold text-gold mb-4">
                Timeless Design
              </h3>
              <p className="text-gray-300">
                Our designs transcend trends, focusing on classic silhouettes that remain elegant season after season.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Team Section */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <h2 className="text-3xl font-serif font-bold text-gold mb-8">
            Meet Our Founder
          </h2>
          <div className="max-w-2xl mx-auto">
            <div className="relative w-48 h-48 mx-auto mb-6 rounded-full overflow-hidden">
              <Image
                src="https://picsum.photos/300/300?random=15"
                alt="Founder"
                fill
                className="object-cover"
              />
            </div>
            <h3 className="text-2xl font-serif font-bold text-gold mb-4">
              Elena Marchetti
            </h3>
            <p className="text-gray-300 text-lg leading-relaxed">
              "Fashion should be a celebration of who you are, not a mask to hide behind. At JOOKA, we create pieces that enhance your natural beauty and confidence, allowing your authentic self to shine through."
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  )
}