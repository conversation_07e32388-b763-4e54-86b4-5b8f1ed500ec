'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { CreditCard, Lock, CheckCircle } from 'lucide-react'
import { useCartStore } from '@/store/cartStore'

export default function CheckoutPage() {
  const { items, getTotalPrice, clearCart } = useCartStore()
  const [isProcessing, setIsProcessing] = useState(false)
  const [orderComplete, setOrderComplete] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'United States',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    nameOnCard: ''
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsProcessing(true)
    
    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false)
      setOrderComplete(true)
      clearCart()
    }, 3000)
  }

  if (orderComplete) {
    return (
      <div className="min-h-screen py-16">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <CheckCircle className="w-24 h-24 text-green-500 mx-auto mb-6" />
            <h1 className="text-4xl font-serif font-bold text-gold mb-4">
              Order Confirmed!
            </h1>
            <p className="text-gray-300 text-lg mb-8">
              Thank you for your purchase. You will receive a confirmation email shortly.
            </p>
            <div className="bg-charcoal/50 rounded-lg p-6 max-w-md mx-auto mb-8">
              <h3 className="text-gold font-semibold mb-2">Order #JK-2024-001</h3>
              <p className="text-gray-300">Estimated delivery: 3-5 business days</p>
            </div>
            <button
              onClick={() => window.location.href = '/shop'}
              className="btn-primary"
            >
              Continue Shopping
            </button>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-serif font-bold text-gold mb-8">
            Checkout
          </h1>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Checkout Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Contact Information */}
              <div>
                <h2 className="text-2xl font-serif font-bold text-gold mb-4">
                  Contact Information
                </h2>
                <div className="space-y-4">
                  <input
                    type="email"
                    name="email"
                    placeholder="Email address"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-charcoal border border-gray-600 rounded-md text-white placeholder-gray-400 focus:border-gold focus:outline-none"
                  />
                </div>
              </div>

              {/* Shipping Address */}
              <div>
                <h2 className="text-2xl font-serif font-bold text-gold mb-4">
                  Shipping Address
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input
                    type="text"
                    name="firstName"
                    placeholder="First name"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    required
                    className="px-4 py-3 bg-charcoal border border-gray-600 rounded-md text-white placeholder-gray-400 focus:border-gold focus:outline-none"
                  />
                  <input
                    type="text"
                    name="lastName"
                    placeholder="Last name"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    required
                    className="px-4 py-3 bg-charcoal border border-gray-600 rounded-md text-white placeholder-gray-400 focus:border-gold focus:outline-none"
                  />
                  <input
                    type="text"
                    name="address"
                    placeholder="Address"
                    value={formData.address}
                    onChange={handleInputChange}
                    required
                    className="md:col-span-2 px-4 py-3 bg-charcoal border border-gray-600 rounded-md text-white placeholder-gray-400 focus:border-gold focus:outline-none"
                  />
                  <input
                    type="text"
                    name="city"
                    placeholder="City"
                    value={formData.city}
                    onChange={handleInputChange}
                    required
                    className="px-4 py-3 bg-charcoal border border-gray-600 rounded-md text-white placeholder-gray-400 focus:border-gold focus:outline-none"
                  />
                  <input
                    type="text"
                    name="state"
                    placeholder="State"
                    value={formData.state}
                    onChange={handleInputChange}
                    required
                    className="px-4 py-3 bg-charcoal border border-gray-600 rounded-md text-white placeholder-gray-400 focus:border-gold focus:outline-none"
                  />
                  <input
                    type="text"
                    name="zipCode"
                    placeholder="ZIP code"
                    value={formData.zipCode}
                    onChange={handleInputChange}
                    required
                    className="px-4 py-3 bg-charcoal border border-gray-600 rounded-md text-white placeholder-gray-400 focus:border-gold focus:outline-none"
                  />
                  <select
                    name="country"
                    value={formData.country}
                    onChange={handleInputChange}
                    className="px-4 py-3 bg-charcoal border border-gray-600 rounded-md text-white focus:border-gold focus:outline-none"
                  >
                    <option value="United States">United States</option>
                    <option value="Canada">Canada</option>
                    <option value="United Kingdom">United Kingdom</option>
                  </select>
                </div>
              </div>

              {/* Payment Information */}
              <div>
                <h2 className="text-2xl font-serif font-bold text-gold mb-4 flex items-center">
                  <CreditCard className="w-6 h-6 mr-2" />
                  Payment Information
                </h2>
                <div className="space-y-4">
                  <input
                    type="text"
                    name="nameOnCard"
                    placeholder="Name on card"
                    value={formData.nameOnCard}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-charcoal border border-gray-600 rounded-md text-white placeholder-gray-400 focus:border-gold focus:outline-none"
                  />
                  <input
                    type="text"
                    name="cardNumber"
                    placeholder="Card number"
                    value={formData.cardNumber}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-charcoal border border-gray-600 rounded-md text-white placeholder-gray-400 focus:border-gold focus:outline-none"
                  />
                  <div className="grid grid-cols-2 gap-4">
                    <input
                      type="text"
                      name="expiryDate"
                      placeholder="MM/YY"
                      value={formData.expiryDate}
                      onChange={handleInputChange}
                      required
                      className="px-4 py-3 bg-charcoal border border-gray-600 rounded-md text-white placeholder-gray-400 focus:border-gold focus:outline-none"
                    />
                    <input
                      type="text"
                      name="cvv"
                      placeholder="CVV"
                      value={formData.cvv}
                      onChange={handleInputChange}
                      required
                      className="px-4 py-3 bg-charcoal border border-gray-600 rounded-md text-white placeholder-gray-400 focus:border-gold focus:outline-none"
                    />
                  </div>
                </div>
              </div>

              <button
                type="submit"
                disabled={isProcessing}
                className="w-full btn-primary text-lg py-4 flex items-center justify-center space-x-2"
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black"></div>
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <Lock className="w-5 h-5" />
                    <span>Complete Order</span>
                  </>
                )}
              </button>
            </form>
          </motion.div>

          {/* Order Summary */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <div className="bg-charcoal/50 rounded-lg p-6 sticky top-8">
              <h2 className="text-2xl font-serif font-bold text-gold mb-6">
                Order Summary
              </h2>

              <div className="space-y-4 mb-6">
                {items.map((item) => (
                  <div key={`${item.id}-${item.size}-${item.color}`} className="flex justify-between">
                    <div>
                      <p className="text-white">{item.name}</p>
                      <p className="text-sm text-gray-400">
                        {item.size && `Size: ${item.size}`} {item.color && `Color: ${item.color}`} Qty: {item.quantity}
                      </p>
                    </div>
                    <p className="text-gold font-semibold">
                      ${(item.price * item.quantity).toFixed(2)}
                    </p>
                  </div>
                ))}
              </div>

              <div className="space-y-2 mb-6 border-t border-gray-600 pt-4">
                <div className="flex justify-between">
                  <span className="text-gray-300">Subtotal</span>
                  <span className="text-gold">${getTotalPrice().toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Shipping</span>
                  <span className="text-gold">Free</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Tax</span>
                  <span className="text-gold">${(getTotalPrice() * 0.08).toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-lg font-semibold border-t border-gray-600 pt-2">
                  <span className="text-gold">Total</span>
                  <span className="text-gold">${(getTotalPrice() * 1.08).toFixed(2)}</span>
                </div>
              </div>

              <div className="text-sm text-gray-400 text-center">
                <p className="flex items-center justify-center mb-2">
                  <Lock className="w-4 h-4 mr-1" />
                  Secure checkout powered by Stripe
                </p>
                <p>Your payment information is encrypted and secure.</p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}