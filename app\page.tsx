'use client'

import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import ProductCard from '@/components/ProductCard'
import JookaHeroDemo from '@/components/ui/jooka-hero-demo'
import CategoriesShowcase from '@/components/CategoriesShowcase'
import TestimonialsSection from '@/components/TestimonialsSection'

// Mock featured products data
const featuredProducts = [
  {
    id: '1',
    name: 'Silk Evening Dress',
    price: 299,
    image: 'https://via.placeholder.com/400x500/1a1a1a/d4af37?text=Silk+Evening+Dress',
    category: 'Dresses'
  },
  {
    id: '2',
    name: 'Cashmere Blazer',
    price: 459,
    image: 'https://via.placeholder.com/400x500/1a1a1a/d4af37?text=Cashmere+Blazer',
    category: 'Outerwear'
  },
  {
    id: '3',
    name: 'Pearl Necklace',
    price: 189,
    image: 'https://via.placeholder.com/400x500/1a1a1a/d4af37?text=Pearl+Necklace',
    category: 'Accessories'
  },
  {
    id: '4',
    name: 'Leather Handbag',
    price: 329,
    image: 'https://via.placeholder.com/400x500/1a1a1a/d4af37?text=Leather+Handbag',
    category: 'Bags'
  }
]

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <JookaHeroDemo />

      {/* Featured Products Section */}
      <section className="relative py-24 px-8 md:px-12">
        {/* Clean Background */}
        <div className="absolute inset-0 bg-gradient-to-b from-black via-black/98 to-black" />

        <div className="relative z-10 max-w-7xl mx-auto">
          {/* Section Header */}
          <motion.div
            className="text-center mb-20"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: true }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="inline-block"
            >
              <span className="text-sm font-medium tracking-[0.2em] text-gold/60 uppercase mb-4 block">
                Curated Excellence
              </span>
            </motion.div>

            <motion.h2
              className="text-5xl md:text-6xl lg:text-7xl font-serif font-light text-gold mb-8 tracking-tight"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
            >
              Featured
              <span className="block text-4xl md:text-5xl lg:text-6xl text-ivory/90 font-light italic">
                Collection
              </span>
            </motion.h2>

            <motion.div
              initial={{ opacity: 0, width: 0 }}
              whileInView={{ opacity: 1, width: "4rem" }}
              transition={{ duration: 0.8, delay: 0.5 }}
              viewport={{ once: true }}
              className="h-px bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mb-8"
            />

            <motion.p
              className="text-lg md:text-xl text-ivory/70 max-w-3xl mx-auto leading-relaxed font-light"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              viewport={{ once: true }}
            >
              Discover our carefully curated selection of luxury pieces that embody
              <span className="text-gold/80 font-medium"> timeless elegance</span> and
              <span className="text-gold/80 font-medium"> natural beauty</span>.
            </motion.p>
          </motion.div>

          {/* Products Grid */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            viewport={{ once: true }}
          >
            {featuredProducts.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 60, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.8,
                  delay: 0.9 + index * 0.15,
                  ease: "easeOut"
                }}
                viewport={{ once: true }}
                className="group"
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </motion.div>

          {/* Call to Action */}
          <motion.div
            className="text-center mt-20"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.4 }}
            viewport={{ once: true }}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              <Link
                href="/shop"
                className="inline-flex items-center px-8 py-4 text-sm font-medium tracking-[0.1em] text-gold border-2 border-gold/30 hover:border-gold hover:bg-gold hover:text-black transition-all duration-500 uppercase group"
              >
                <span>Explore Collection</span>
                <motion.span
                  className="ml-2 group-hover:ml-4 transition-all duration-300"
                  initial={{ x: 0 }}
                  whileHover={{ x: 4 }}
                >
                  →
                </motion.span>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Categories Showcase Section */}
      <CategoriesShowcase />

      {/* Brand Story Section */}
      <section className="relative py-24 px-8 md:px-12">
        {/* Clean Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-black via-charcoal/20 to-black" />

        <div className="relative z-10 max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 lg:gap-20 items-center">
            {/* Content */}
            <motion.div
              className="space-y-8"
              initial={{ opacity: 0, x: -60 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <span className="text-sm font-medium tracking-[0.2em] text-gold/60 uppercase mb-4 block">
                  Heritage
                </span>
                <h2 className="text-5xl md:text-6xl font-serif font-light text-gold mb-6 tracking-tight">
                  Our
                  <span className="block text-4xl md:text-5xl text-ivory/90 font-light italic">
                    Story
                  </span>
                </h2>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, width: 0 }}
                whileInView={{ opacity: 1, width: "4rem" }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="h-px bg-gradient-to-r from-gold via-gold/50 to-transparent"
              />

              <motion.div
                className="space-y-6"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                viewport={{ once: true }}
              >
                <p className="text-xl md:text-2xl text-ivory/80 leading-relaxed font-light">
                  JOOKA was born from a vision to create fashion that transcends trends and embraces
                  <span className="text-gold/80 font-medium"> timeless elegance</span>.
                </p>
                <p className="text-lg text-ivory/70 leading-relaxed font-light">
                  Our commitment to natural beauty and sophisticated design reflects in every piece we create.
                  Each garment is crafted with meticulous attention to detail, using only the finest materials
                  sourced ethically from around the world.
                </p>
                <p className="text-base text-ivory/60 leading-relaxed font-light">
                  We believe that true luxury lies not in excess, but in the perfect harmony of
                  craftsmanship, sustainability, and timeless design.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.7 }}
                viewport={{ once: true }}
              >
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                >
                  <Link
                    href="/about"
                    className="inline-flex items-center px-8 py-4 text-sm font-medium tracking-[0.1em] text-gold border-2 border-gold/30 hover:border-gold hover:bg-gold hover:text-black transition-all duration-500 uppercase group"
                  >
                    <span>Discover Our Heritage</span>
                    <motion.span
                      className="ml-2 group-hover:ml-4 transition-all duration-300"
                      initial={{ x: 0 }}
                      whileHover={{ x: 4 }}
                    >
                      →
                    </motion.span>
                  </Link>
                </motion.div>
              </motion.div>
            </motion.div>

            {/* Image */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, x: 60 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              <div className="relative h-[500px] lg:h-[600px] overflow-hidden">
                <Image
                  src="https://via.placeholder.com/600x700/1a1a1a/d4af37?text=JOOKA+Story"
                  alt="JOOKA Story - Craftsmanship and Elegance"
                  fill
                  className="object-cover"
                />

                {/* Image Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />

                {/* Decorative Border */}
                <motion.div
                  className="absolute inset-0 border-2 border-gold/20"
                  initial={{ opacity: 0, scale: 1.1 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8, delay: 0.3 }}
                  viewport={{ once: true }}
                />

                {/* Floating Stats */}
                <motion.div
                  className="absolute bottom-8 left-8 bg-black/70 backdrop-blur-sm p-6 border border-gold/30"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                  viewport={{ once: true }}
                >
                  <div className="text-center">
                    <div className="text-2xl font-serif font-bold text-gold mb-1">2019</div>
                    <div className="text-sm text-ivory/70 font-light tracking-wide">Founded</div>
                  </div>
                </motion.div>

                <motion.div
                  className="absolute top-8 right-8 bg-black/70 backdrop-blur-sm p-6 border border-gold/30"
                  initial={{ opacity: 0, y: -20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1 }}
                  viewport={{ once: true }}
                >
                  <div className="text-center">
                    <div className="text-2xl font-serif font-bold text-gold mb-1">100%</div>
                    <div className="text-sm text-ivory/70 font-light tracking-wide">Sustainable</div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <TestimonialsSection />
    </div>
  )
}