"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/TestimonialsSection.tsx":
/*!********************************************!*\
  !*** ./components/TestimonialsSection.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestimonialsSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst testimonials = [\n    {\n        id: \"1\",\n        name: \"Isabella Chen\",\n        role: \"Fashion Director\",\n        location: \"New York\",\n        image: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?auto=format&fit=crop&w=200&h=200\",\n        rating: 5,\n        quote: \"JOOKA has redefined luxury for me. Every piece tells a story of exceptional craftsmanship and timeless elegance. The attention to detail is simply unmatched.\",\n        product: \"Silk Evening Dress\"\n    },\n    {\n        id: \"2\",\n        name: \"Sophia Martinez\",\n        role: \"Creative Executive\",\n        location: \"Los Angeles\",\n        image: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=200&h=200\",\n        rating: 5,\n        quote: \"The quality and sophistication of JOOKA pieces are extraordinary. I feel confident and elegant every time I wear their designs. Truly investment pieces.\",\n        product: \"Cashmere Blazer\"\n    },\n    {\n        id: \"3\",\n        name: \"Emma Thompson\",\n        role: \"Art Curator\",\n        location: \"London\",\n        image: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?auto=format&fit=crop&w=200&h=200\",\n        rating: 5,\n        quote: \"JOOKA understands the modern woman. Their designs are both contemporary and timeless, perfect for my lifestyle. The natural elegance is evident in every detail.\",\n        product: \"Pearl Necklace\"\n    }\n];\nconst StarRating = (param)=>{\n    let { rating } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex space-x-1\",\n        children: [\n            ...Array(5)\n        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-4 h-4 \".concat(i < rating ? \"text-gold fill-gold\" : \"text-gold/30\")\n            }, i, false, {\n                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n_c = StarRating;\nfunction TestimonialsSection() {\n    _s();\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutoPlaying, setIsAutoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAutoPlaying) return;\n        const interval = setInterval(()=>{\n            setCurrentIndex((prev)=>(prev + 1) % testimonials.length);\n        }, 5000);\n        return ()=>clearInterval(interval);\n    }, [\n        isAutoPlaying\n    ]);\n    const nextTestimonial = ()=>{\n        setCurrentIndex((prev)=>(prev + 1) % testimonials.length);\n        setIsAutoPlaying(false);\n    };\n    const prevTestimonial = ()=>{\n        setCurrentIndex((prev)=>(prev - 1 + testimonials.length) % testimonials.length);\n        setIsAutoPlaying(false);\n    };\n    const currentTestimonial = testimonials[currentIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative py-24 px-8 md:px-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-b from-black via-black/98 to-black\"\n            }, void 0, false, {\n                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"text-center mb-20\",\n                        initial: {\n                            opacity: 0,\n                            y: 40\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                className: \"text-sm font-medium tracking-[0.2em] text-gold/60 uppercase mb-4 block\",\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.9\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: \"Testimonials\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h2, {\n                                className: \"text-5xl md:text-6xl lg:text-7xl font-serif font-light text-gold mb-8 tracking-tight\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.3\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    \"What Our\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-4xl md:text-5xl lg:text-6xl text-ivory/90 font-light italic\",\n                                        children: \"Clients Say\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    width: 0\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    width: \"4rem\"\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.5\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"h-px bg-gradient-to-r from-transparent via-gold to-transparent mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                                mode: \"wait\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 50\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        y: -50\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        ease: \"easeOut\"\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            className: \"flex justify-center mb-8\",\n                                            initial: {\n                                                scale: 0\n                                            },\n                                            animate: {\n                                                scale: 1\n                                            },\n                                            transition: {\n                                                duration: 0.5,\n                                                delay: 0.2\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-12 h-12 text-gold/30\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.blockquote, {\n                                            className: \"text-2xl md:text-3xl lg:text-4xl font-serif font-light text-ivory leading-relaxed max-w-4xl mx-auto mb-12\",\n                                            initial: {\n                                                opacity: 0\n                                            },\n                                            animate: {\n                                                opacity: 1\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.3\n                                            },\n                                            children: [\n                                                '\"',\n                                                currentTestimonial.quote,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            className: \"flex flex-col md:flex-row items-center justify-center space-y-6 md:space-y-0 md:space-x-8\",\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-20 h-20 rounded-full overflow-hidden border-2 border-gold/30\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: currentTestimonial.image,\n                                                        alt: currentTestimonial.name,\n                                                        fill: true,\n                                                        className: \"object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center md:text-left space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-xl font-serif font-medium text-gold\",\n                                                            children: currentTestimonial.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-ivory/70 font-light\",\n                                                            children: [\n                                                                currentTestimonial.role,\n                                                                \" • \",\n                                                                currentTestimonial.location\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StarRating, {\n                                                            rating: currentTestimonial.rating\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gold/60 font-medium\",\n                                                            children: [\n                                                                \"Purchased: \",\n                                                                currentTestimonial.product\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, currentIndex, true, {\n                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center space-x-4 mt-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                        onClick: prevTestimonial,\n                                        className: \"p-3 rounded-full border border-gold/30 hover:border-gold hover:bg-gold/10 transition-all duration-300\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 text-gold\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: testimonials.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setCurrentIndex(index);\n                                                    setIsAutoPlaying(false);\n                                                },\n                                                className: \"w-2 h-2 rounded-full transition-all duration-300 \".concat(index === currentIndex ? \"bg-gold w-8\" : \"bg-gold/30\")\n                                            }, index, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                        onClick: nextTestimonial,\n                                        className: \"p-3 rounded-full border border-gold/30 hover:border-gold hover:bg-gold/10 transition-all duration-300\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5 text-gold\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\TestimonialsSection.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(TestimonialsSection, \"GaMjhoJL+kq9QyJhTWKgFkGeqfI=\");\n_c1 = TestimonialsSection;\nvar _c, _c1;\n$RefreshReg$(_c, \"StarRating\");\n$RefreshReg$(_c1, \"TestimonialsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/TestimonialsSection.tsx\n"));

/***/ })

});