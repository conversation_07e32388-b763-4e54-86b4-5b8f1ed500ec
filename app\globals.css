@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Inter:wght@300;400;500;600&family=Cormorant+Garamond:wght@300;400;500&display=swap');

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Inter', sans-serif;
    background-color: #111111;
    color: #F5F5F5;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    color: #D4AF37;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gold text-black px-6 py-3 rounded-md font-semibold hover:bg-charcoal hover:text-gold border-2 border-gold transition-all duration-300;
  }
  
  .btn-secondary {
    @apply border-2 border-gold text-gold px-6 py-3 rounded-md font-semibold hover:bg-gold hover:text-black transition-all duration-300;
  }
  
  .card-product {
    @apply bg-charcoal rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105;
  }
}