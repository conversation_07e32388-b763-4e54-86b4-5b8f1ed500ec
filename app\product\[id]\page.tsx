'use client'

import { useState } from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { Minus, Plus, Heart, Share2 } from 'lucide-react'
import { useCartStore } from '@/store/cartStore'
import ProductCard from '@/components/ProductCard'

// Mock product data
const getProduct = (id: string) => ({
  id,
  name: 'Silk Evening Dress',
  price: 299,
  images: [
    'https://picsum.photos/600/800?random=11',
    'https://picsum.photos/600/800?random=12',
    'https://picsum.photos/600/800?random=13',
  ],
  description: 'Crafted from the finest silk, this evening dress embodies timeless elegance with its flowing silhouette and luxurious feel. Perfect for special occasions where you want to make a lasting impression.',
  details: [
    '100% Pure Silk',
    'Dry Clean Only',
    'Made in Italy',
    'Model is 5\'8" wearing size S'
  ],
  sizes: ['XS', 'S', 'M', 'L', 'XL'],
  colors: ['Black', 'Navy', 'Emerald'],
  category: 'Dresses'
})

const relatedProducts = [
  { id: '2', name: 'Cashmere Blazer', price: 459, image: 'https://picsum.photos/400/500?random=4', category: 'Outerwear' },
  { id: '3', name: 'Pearl Necklace', price: 189, image: 'https://picsum.photos/400/500?random=5', category: 'Accessories' },
  { id: '4', name: 'Leather Handbag', price: 329, image: 'https://picsum.photos/400/500?random=6', category: 'Bags' },
]

export default function ProductPage({ params }: { params: { id: string } }) {
  const [selectedImage, setSelectedImage] = useState(0)
  const [selectedSize, setSelectedSize] = useState('')
  const [selectedColor, setSelectedColor] = useState('')
  const [quantity, setQuantity] = useState(1)
  
  const addItem = useCartStore((state) => state.addItem)
  const product = getProduct(params.id)

  const handleAddToCart = () => {
    if (!selectedSize || !selectedColor) {
      alert('Please select size and color')
      return
    }
    
    for (let i = 0; i < quantity; i++) {
      addItem({
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.images[0],
        size: selectedSize,
        color: selectedColor,
      })
    }
  }

  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Product Images */}
          <div className="space-y-4">
            <motion.div
              className="relative aspect-[3/4] overflow-hidden rounded-lg"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6 }}
            >
              <Image
                src={product.images[selectedImage]}
                alt={product.name}
                fill
                className="object-cover"
              />
            </motion.div>
            
            <div className="flex space-x-2 overflow-x-auto">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`relative w-20 h-24 flex-shrink-0 rounded-md overflow-hidden border-2 ${
                    selectedImage === index ? 'border-gold' : 'border-transparent'
                  }`}
                >
                  <Image
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div>
              <p className="text-gold/70 text-sm uppercase tracking-wide mb-2">
                {product.category}
              </p>
              <h1 className="text-3xl md:text-4xl font-serif font-bold text-gold mb-4">
                {product.name}
              </h1>
              <p className="text-2xl font-semibold text-gold mb-6">
                ${product.price}
              </p>
            </div>

            <p className="text-gray-300 leading-relaxed">
              {product.description}
            </p>

            {/* Size Selection */}
            <div>
              <h3 className="text-lg font-semibold text-gold mb-3">Size</h3>
              <div className="flex flex-wrap gap-2">
                {product.sizes.map((size) => (
                  <button
                    key={size}
                    onClick={() => setSelectedSize(size)}
                    className={`px-4 py-2 border rounded-md transition-all ${
                      selectedSize === size
                        ? 'border-gold bg-gold text-black'
                        : 'border-gray-600 text-gray-300 hover:border-gold'
                    }`}
                  >
                    {size}
                  </button>
                ))}
              </div>
            </div>

            {/* Color Selection */}
            <div>
              <h3 className="text-lg font-semibold text-gold mb-3">Color</h3>
              <div className="flex flex-wrap gap-2">
                {product.colors.map((color) => (
                  <button
                    key={color}
                    onClick={() => setSelectedColor(color)}
                    className={`px-4 py-2 border rounded-md transition-all ${
                      selectedColor === color
                        ? 'border-gold bg-gold text-black'
                        : 'border-gray-600 text-gray-300 hover:border-gold'
                    }`}
                  >
                    {color}
                  </button>
                ))}
              </div>
            </div>

            {/* Quantity */}
            <div>
              <h3 className="text-lg font-semibold text-gold mb-3">Quantity</h3>
              <div className="flex items-center space-x-4">
                <div className="flex items-center border border-gray-600 rounded-md">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="p-2 hover:bg-gray-800 transition-colors"
                  >
                    <Minus className="w-4 h-4 text-gold" />
                  </button>
                  <span className="px-4 py-2 text-gold">{quantity}</span>
                  <button
                    onClick={() => setQuantity(quantity + 1)}
                    className="p-2 hover:bg-gray-800 transition-colors"
                  >
                    <Plus className="w-4 h-4 text-gold" />
                  </button>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-4">
              <button
                onClick={handleAddToCart}
                className="w-full btn-primary text-lg py-4"
              >
                Add to Cart
              </button>
              
              <div className="flex space-x-4">
                <button className="flex items-center space-x-2 text-gold hover:text-white transition-colors">
                  <Heart className="w-5 h-5" />
                  <span>Add to Wishlist</span>
                </button>
                <button className="flex items-center space-x-2 text-gold hover:text-white transition-colors">
                  <Share2 className="w-5 h-5" />
                  <span>Share</span>
                </button>
              </div>
            </div>

            {/* Product Details */}
            <div className="border-t border-gray-700 pt-6">
              <h3 className="text-lg font-semibold text-gold mb-3">Details</h3>
              <ul className="space-y-2 text-gray-300">
                {product.details.map((detail, index) => (
                  <li key={index}>• {detail}</li>
                ))}
              </ul>
            </div>
          </motion.div>
        </div>

        {/* Related Products */}
        <section className="mt-16">
          <h2 className="text-3xl font-serif font-bold text-gold mb-8 text-center">
            You May Also Like
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </section>
      </div>
    </div>
  )
}